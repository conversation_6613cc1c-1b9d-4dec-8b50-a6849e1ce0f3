<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="h-full grid grid-cols-1 grid-rows-[1fr] relative">
    <l-fab axis="xy" v-model:offset="cartOffset" magnetic="x">
      <template #default>
        <image
          class="w-120rpx h-120rpx absolute right-0 top-50% translate-y-[-50%] trasform"
          src="@/static/images/callPhone.png"
        ></image>
      </template>
    </l-fab>
    <z-paging
      ref="paging"
      empty-view-text="没有聊天记录哦~"
      v-model="dataList"
      use-chat-record-mode
      chat-adjust-position-offset="0px"
      inside-more
      :paging-style="{
        'background-color': '#F6F6F6',
        'padding-bottom': `${bottom || 15}px`,
        'box-sizing': 'border-box',
      }"
      auto-to-bottom-when-chat
      :default-page-size="10"
      @query="queryList"
      :fixed="false"
    >
      <template #top>
        <div>
          <wd-navbar fixed placeholder safeAreaInsetTop left-arrow @click-left="handleClickLeft">
            <template #title>{{ nickName }}({{ isOnline }})</template>
          </wd-navbar>
          <!-- <div
            v-if="paramsOptions.shopSupplierId && paramsOptions.shopSupplierId !== '0'"
            class="flex items-center justify-between bg-#ffffff p-20rpx box-border"
          >
            <div class="flex items-center gap-x-10rpx">
              <div class="i-carbon-store"></div>
              <div class="text-28rpx text-#333333">{{ storeDetial?.detail?.name }}</div>
            </div>
            <wd-button @click="goShop" size="small" custom-class="!m-unset !min-w-unset">
              进店
            </wd-button>
          </div> -->
        </div>
      </template>

      <!-- for循环渲染聊天记录列表 -->
      <div class="p24rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-40rpx">
        <view v-for="item in dataList" :key="item?.chatId" style="position: relative">
          <!-- 如果要给聊天item添加长按的popup，请在popup标签上写style="transform: scaleY(-1);"，注意style="transform: scaleY(-1);"不要写在最外层，否则可能导致popup被其他聊天item盖住 -->
          <!-- <view class="popup" style="transform: scaleY(-1);">popUp</view> -->

          <!-- style="transform: scaleY(-1)"必须写，否则会导致列表倒置 -->
          <!-- 注意不要直接在chat-item组件标签上设置style，因为在微信小程序中是无效的，请包一层view -->
          <!-- sendType   1客服  2用户 -->
          <view style="transform: scaleY(-1)">
            <!-- 用户 -->
            <div
              v-if="item?.sendType === 2"
              class="grid grid-cols-[1fr_auto] justify-items-end gap-x-16rpx"
            >
              <!-- 普通对话 -->
              <div
                v-if="item?.msgType === 0"
                class="rd-10rpx bg-#ffffff break-all px22rpx py18rpx h-fit max-w-80%"
              >
                {{ item?.content }}
              </div>
              <!-- 图片对话 -->
              <div v-if="item?.msgType === 1" class="rd-10rpx bg-#ffffff px22rpx py18rpx h-fit">
                <wd-img
                  :src="item?.content"
                  width="200rpx"
                  enable-preview
                  mode="aspectFill"
                  height="200rpx"
                  radius="10rpx"
                  :preview-src="item?.content"
                />
              </div>
              <!-- 商品对话 -->
              <div v-if="item?.msgType === 2" class="rd-10rpx bg-#ffffff px22rpx py18rpx h-fit">
                <div class="flex items-center gap-x-20rpx h-full min-w-502rpx">
                  <wd-img
                    width="162rpx"
                    radius="20rpx"
                    mode="aspectFill"
                    height="162rpx"
                    :src="item?.contentJson?.productImg"
                  />
                  <div class="flex flex-col h-162rpx gap-y-20rpx">
                    <span class="text-24rpx text-#333333">
                      {{ item?.contentJson?.productName }}
                    </span>
                    <div class="flex items-baseline">
                      <span class="text-20rpx text-#FF7D26">¥</span>
                      <span class="text-28rpx text-#FF7D26 text-20rpx">
                        {{ item?.contentJson?.productPrice?.split('.')?.[0] }}
                      </span>
                      <span class="text-20rpx text-#FF7D26">
                        .{{ item?.contentJson?.productPrice?.split('.')?.[1] }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 订单对话 -->
              <div v-if="item?.msgType === 3" class="rd-10rpx bg-#ffffff px22rpx py18rpx h-fit">
                <div class="flex flex-col gap-y-20rpx">
                  <span class="font-bold text-24rpx text-#333333">你正在咨询的订单</span>
                  <div class="flex items-center gap-x-20rpx">
                    <wd-img
                      width="150rpx"
                      height="150rpx"
                      radius="10rpx"
                      :src="item?.contentJson?.productImg"
                    />
                    <div class="flex flex-col h-150rpx gap-y-20rpx">
                      <span class="line-clamp-1 text-24rpx text-#333333">
                        {{ item?.contentJson?.productName }}
                      </span>
                      <div class="flex text-24rpx items-center text-#666666">
                        共计{{ item?.contentJson?.orderNum }}件商品：合计￥{{
                          item?.contentJson?.orderPrice
                        }}
                      </div>
                    </div>
                  </div>
                  <div class="text-24rpx text-#666666">订单号:{{ item?.contentJson?.orderNo }}</div>
                  <div class="text-24rpx text-#666666">
                    创建时间:{{ item?.contentJson?.createTime }}
                  </div>
                  <div>
                    <wd-button
                      @click="goOrderDetail(item?.contentJson?.orderId)"
                      custom-class="!h56rpx !min-w-unset !m-unset"
                    >
                      查看
                    </wd-button>
                  </div>
                </div>
              </div>
              <wd-img
                :src="userAvatar"
                width="80rpx"
                height="80rpx"
                radius="10rpx"
                mode="aspectFill"
              />
            </div>
            <!-- 客服 -->
            <div
              v-if="item?.sendType === 1"
              class="grid grid-cols-[auto_1fr] justify-items-start gap-x-16rpx"
            >
              <wd-img
                :src="serviceAvatar"
                width="80rpx"
                height="80rpx"
                radius="10rpx"
                mode="aspectFill"
              />
              <div class="flex flex-col gap-y-8rpx">
                <span class="text-24rpx text-#666666">{{ item?.nickName }}</span>
                <!-- 普通对话 -->
                <div
                  v-if="item?.msgType === 0"
                  class="rd-10rpx bg-#ffffff break-all px22rpx py18rpx h-fit max-w-80%"
                >
                  {{ item?.content }}
                </div>
                <!-- 图片对话 -->
                <div v-if="item?.msgType === 1" class="rd-10rpx bg-#ffffff px22rpx py18rpx h-fit">
                  <wd-img
                    :src="item?.content"
                    width="200rpx"
                    height="200rpx"
                    enable-preview
                    mode="aspectFill"
                    radius="10rpx"
                    :preview-src="item?.content"
                  />
                </div>
                <!-- 商品对话 -->
                <div v-if="item?.msgType === 2" class="rd-10rpx bg-#ffffff px22rpx py18rpx h-fit">
                  <div class="flex items-center gap-x-20rpx h-full min-w-502rpx">
                    <wd-img
                      width="162rpx"
                      radius="20rpx"
                      mode="aspectFill"
                      height="162rpx"
                      :src="item?.contentJson?.productImg"
                    />
                    <div class="flex flex-col h-162rpx gap-y-20rpx">
                      <span class="text-24rpx text-#333333">
                        {{ item?.contentJson?.productName }}
                      </span>
                      <div class="flex items-baseline">
                        <span class="text-20rpx text-#FF7D26">¥</span>
                        <span class="text-28rpx text-#FF7D26 text-20rpx">
                          {{ item?.contentJson?.productPrice?.split('.')?.[0] }}
                        </span>
                        <span class="text-20rpx text-#FF7D26">
                          .{{ item?.contentJson?.productPrice?.split('.')?.[1] }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 订单对话 -->
                <div v-if="item?.msgType === 3" class="rd-10rpx bg-#ffffff px22rpx py18rpx h-fit">
                  <div class="flex flex-col gap-y-20rpx">
                    <span class="font-bold text-24rpx text-#333333">你正在咨询的订单</span>
                    <div class="flex items-center gap-x-20rpx">
                      <wd-img
                        width="150rpx"
                        height="150rpx"
                        radius="10rpx"
                        :src="item?.contentJson?.productImg"
                      />
                      <div class="flex flex-col h-150rpx gap-y-20rpx">
                        <span class="line-clamp-1 text-24rpx text-#333333">
                          {{ item?.contentJson?.productName }}
                        </span>
                        <div class="flex text-24rpx items-center text-#666666">
                          共计{{ item?.contentJson?.orderNum }}件商品：合计￥{{
                            item?.contentJson?.orderPrice
                          }}
                        </div>
                      </div>
                    </div>
                    <div class="text-24rpx text-#666666">
                      订单号:{{ item?.contentJson?.orderNo }}
                    </div>
                    <div class="text-24rpx text-#666666">
                      创建时间:{{ item?.contentJson?.createTime }}
                    </div>
                    <div>
                      <wd-button
                        @click="goOrderDetail(item?.contentJson?.orderId)"
                        custom-class="!h56rpx !min-w-unset !m-unset"
                      >
                        查看
                      </wd-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="item?.sendType === 'product' && isProduct"
              class="grid grid-cols-1 justify-items-center"
            >
              <div
                class="flex flex-col w-502rpx box-border px22rpx py18rpx rd-10rpx bg-#ffffff gap-y-20rpx"
              >
                <div class="flex items-center gap-x-20rpx h-full">
                  <wd-img
                    width="162rpx"
                    radius="20rpx"
                    mode="aspectFill"
                    height="162rpx"
                    :src="productDetail.detail?.image[0]?.filePath"
                  />
                  <div class="flex flex-col justify-between h-full">
                    <span class="text-24rpx text-#333333">
                      {{ productDetail?.detail?.productName }}
                    </span>
                    <div class="flex items-baseline">
                      <span class="text-20rpx text-#FF7D26">¥</span>
                      <span class="text-28rpx text-#FF7D26 text-20rpx">
                        {{ productDetail?.detail?.productPrice?.split('.')?.[0] }}
                      </span>
                      <span class="text-20rpx text-#FF7D26">
                        .{{ productDetail?.detail?.productPrice?.split('.')?.[1] }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex items-center gap-x-20rpx justify-end">
                  <wd-button @click="sendProduct" custom-class="!h56rpx !min-w-unset !m-unset">
                    发送商品
                  </wd-button>
                </div>
              </div>
            </div>
            <div
              v-if="item?.sendType === 'order' && isOrder"
              class="grid grid-cols-1 justify-items-center"
            >
              <div
                class="flex flex-col w-502rpx box-border px22rpx py18rpx rd-10rpx bg-#ffffff gap-y-20rpx"
              >
                <div class="flex gap-x-20rpx">
                  <wd-img
                    width="150rpx"
                    height="150rpx"
                    radius="10rpx"
                    :src="orderDetial?.detail?.product[0]?.productImage"
                  />
                  <div class="flex flex-col">
                    <span class="font-bold text-24rpx text-#333333">你可能想咨询该订单</span>

                    <div class="flex flex-col gap-y-20rpx">
                      <div class="flex items-center text-24rpx text-#666666">
                        共计{{ orderDetial?.detail?.product?.length }}件商品：合计￥{{
                          orderDetial?.detail?.orderPrice
                        }}
                      </div>
                    </div>
                    <div class="self-end">
                      <wd-button @click="sendOrder" custom-class="!h56rpx !min-w-unset !m-unset">
                        发送订单
                      </wd-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </view>
        </view>
      </div>

      <!-- 底部聊天输入框 -->
      <template #bottom>
        <div class="px16rpx box-border w-full grid grid-cols-[1fr_auto] gap-x-20rpx">
          <div class="w-full h-60rpx rd-20rpx bg-[#ffffff] px20rpx box-border flex items-center">
            <wd-input
              custom-class="!bg-transparent !w-full"
              type="text"
              no-border
              confirm-type="send"
              @confirm="sendContent"
              v-model="sendInputVal"
              placeholder="请输入您要咨询的内容"
            />
          </div>
          <wd-button @click="sendContent" custom-class="!h-60rpx !w-120rpx !min-w-unset">
            发送
          </wd-button>
        </div>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import {
  fetchChatRecord,
  fetchChatInfo,
  fetchRecGoodsDetail,
  fetchSupplierInfo,
  UserOrderDetail,
} from '@/service'
import { getVisitcode } from '@/utils'
import { useUserStore } from '@/store'
import dayjs from 'dayjs'

const userStore = useUserStore()

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

// url参数
const paramsOptions = ref({
  youUserId: 0,
  myUserId: 0,
  shopSupplierId: '',
  productId: '',
  orderId: '',
  nickName: '',
})
onLoad(async (e) => {
  // 保存参数
  paramsOptions.value = {
    youUserId: parseInt(e?.userId || '0'),
    nickName: e?.nickName ?? '客服',
    productId: e?.productId ?? '',
    orderId: e?.orderId ?? '',
    shopSupplierId: e?.shopSupplierId ?? '',
    myUserId: userStore?.userInfo?.userId,
  }
  console.log('🚀 ~ onLoad ~ e:', paramsOptions.value)
  // 店铺
  // if (paramsOptions.value?.shopSupplierId) {
  //   await storeRun()
  // }
})

const socketUrl = ref('')
const socketTask = ref(null)
const isOpenSocket = ref(false)
const intervalId = ref(null) // 心跳定时器
const isLive = ref(false) //初次进入
// 获取胶囊按钮信息（小程序环境）
let menuButtonInfo = { width: 0, height: 0, top: 0, right: 0 }
try {
  // #ifdef MP-WEIXIN
  menuButtonInfo = uni.getMenuButtonBoundingClientRect()
  // #endif
} catch (e) {
  console.log('获取胶囊信息失败:', e)
}

const { windowHeight, windowWidth, platform, safeAreaInsets } = uni.getSystemInfoSync()
const tabBarHeight = bottom || 15
const cartIconSize = uni.upx2px(120) // 购物车图标大小
const minY = (safeAreaInsets?.top || 0) + 20 // 最小 Y 坐标，不能超出状态栏
const maxY = windowHeight - tabBarHeight - cartIconSize - minY - 10 // 最大 Y 坐标，确保不被 tabBar 遮挡
const initialX = windowWidth - cartIconSize - 10 // 图标大小  + 10px边距
const cartOffset = ref([initialX, Math.min(windowHeight / 1.5, maxY)])
// 监听购物车位置变化，限制拖拽边界
let isAdjusting = false
watch(
  cartOffset,
  (newOffset) => {
    if (isAdjusting) return // 防止无限循环

    const [x, y] = newOffset
    let adjustedX = x
    let adjustedY = y
    let needsAdjustment = false

    // 限制 Y 轴最小值，不能超出屏幕顶部
    if (y < minY) {
      console.log('触发顶部限制:', y, '<', minY)
      adjustedY = minY
      needsAdjustment = true
    }

    // 限制 Y 轴最大值，确保不会被 tabBar 遮挡
    if (y > maxY) {
      console.log('触发底部限制:', y, '>', maxY)
      adjustedY = maxY
      needsAdjustment = true
    }

    // 限制 X 轴范围，确保徽标不被屏幕边缘遮盖
    const badgeSize = 20 // 徽标大小估算
    const minX = badgeSize // 左边界：给徽标留出空间
    const maxX = windowWidth - cartIconSize - badgeSize // 右边界：图标大小 + 徽标空间

    if (x < minX) {
      console.log('触发左边界限制:', x, '<', minX)
      adjustedX = minX
      needsAdjustment = true
    }

    if (x > maxX) {
      console.log('触发右边界限制:', x, '>', maxX)
      adjustedX = maxX
      needsAdjustment = true
    }

    // 限制胶囊区域，确保不会遮挡胶囊按钮（只在微信小程序中）
    // #ifdef MP-WEIXIN
    if (menuButtonInfo.width > 0) {
      const capsuleLeft = windowWidth - menuButtonInfo.width - 20 // 胶囊左边界（留20px边距）
      const capsuleTop = menuButtonInfo.top - 10 // 胶囊上边界（留10px边距）
      const capsuleBottom = menuButtonInfo.top + menuButtonInfo.height + 10 // 胶囊下边界（留10px边距）

      // 如果购物车在胶囊区域内，完全禁止拖拽到这个区域
      if (y >= capsuleTop && y <= capsuleBottom && x >= capsuleLeft) {
        console.log('触发胶囊禁区限制:', { x, y, capsuleLeft, capsuleTop, capsuleBottom })
        // 将购物车移到胶囊左侧或下方，选择距离更近的位置
        const distanceToLeft = Math.abs(x - (capsuleLeft - cartIconSize))
        const distanceToBottom = Math.abs(y - (capsuleBottom + 10))

        if (distanceToLeft <= distanceToBottom) {
          // 移到胶囊左侧，确保徽标有空间显示
          const badgeSize = 20
          adjustedX = Math.max(badgeSize, capsuleLeft - cartIconSize - 10)
        } else {
          // 移到胶囊下方
          adjustedY = capsuleBottom + 10
        }
        needsAdjustment = true
      }
    }
    // #endif

    // 如果需要调整，更新位置
    if (needsAdjustment) {
      isAdjusting = true
      nextTick(() => {
        cartOffset.value = [adjustedX, adjustedY]
        setTimeout(() => {
          isAdjusting = false
        }, 100)
      })
    }
  },
  { deep: true },
)

const socketInit = () => {
  if (isOpenSocket.value) {
    return
  }
  socketTask.value = null
  socketTask.value = uni.connectSocket({
    url: socketUrl.value + '/socket?userId=' + userStore?.userInfo?.userId + '&usertype=user',
    success() {
      console.log('Socket连接成功！')
    },
  })
  // 消息的发送和接收必须在正常连接打开中,才能发送或接收【否则会失败】
  socketTask.value.onOpen(() => {
    console.log('WebSocket连接正常打开中...！')
    isOpenSocket.value = true
    // 开始发送心跳
    startHeart()
    startHeart(true)
    // 注：只有连接正常打开中 ，才能正常收到消息
    socketTask.value.onMessage((res) => {
      getNewcontent(res)
    })
  })
  // 这里仅是事件监听【如果socket关闭了会执行】
  socketTask.value.onClose(() => {
    console.log('已经被关闭了')
    //重连机制
    if (!isLive.value) {
      socketTask.value = null
      isOpenSocket.value = false
      clearInterval(intervalId.value)
      !isLive.value && socketInit()
    }
  })
}

const send = (data) => {
  if (isOpenSocket.value) {
    socketTask.value.send({
      data: data,
      success() {},
    })
  } else {
    console.log('处于离线状态')
    socketTask.value = null
    isOpenSocket.value = false
    clearInterval(intervalId.value)
    socketInit()
  }
}

const startHeart = (isLoop?: boolean) => {
  const data = JSON.stringify({
    type: 'ping',
    sendType: 2,
    to: paramsOptions.value.youUserId,
    from: paramsOptions.value.myUserId,
  })
  if (isLoop) {
    intervalId.value = setInterval(() => {
      console.log('发送心跳')
      send(data)
    }, 10000)
  } else {
    console.log('发送心跳')
    send(data)
  }
}

const isOnline = ref('离线')

const getNewcontent = (res: any) => {
  const newData = JSON.parse(res.data)
  if (newData.online === 'off' && !isLive.value) {
    isOnline.value = '离线'
    console.log('对方离线')
  }
  if (newData.online === 'on' && !isLive.value) {
    isOnline.value = '在线'
    console.log('对方在线')
  }

  if (newData.userId === paramsOptions.value.youUserId && newData.content) {
    if (newData.contentJson) {
      newData.content = newData.contentJson
    }
    let item = {
      content: newData.content,
      contentJson: newData.contentJson,
      userId: newData.userId,
      type: newData.type,
      msgType: newData.msgType,
      nickName: nickName.value,
      sendType: 1,
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    }
    console.log('🚀 ~ getNewcontent ~ item:解析数据', item)

    paging.value?.addChatRecordData(item)

    const hasReadData = {
      type: 'read',
      sendType: 2,
      to: paramsOptions.value.youUserId,
      from: paramsOptions.value.myUserId,
    }
    send(JSON.stringify(hasReadData))
  }
}

const closeSocket = () => {
  const data = JSON.stringify({
    type: 'close',
    appId: 10001,
    serviceUserId: paramsOptions.value.youUserId,
    userId: paramsOptions.value.myUserId,
    shopSupplierId: paramsOptions.value.shopSupplierId,
    msgType: 2,
  })
  send(data)
  socketTask.value.close({
    success(res) {
      console.log('关闭成功', res)
    },
    fail(err) {
      console.log('关闭失败', err)
    },
  })
  socketTask.value = null
  isOpenSocket.value = false
  clearInterval(intervalId.value)
}

onBeforeUnmount(() => {
  isLive.value = true
  closeSocket()
})

onShow(() => {
  getChatInfo()
})

// 聊天信息
const { data: chatInfo, run: getChatInfo } = useRequest(() =>
  fetchChatInfo({
    serviceUserId: paramsOptions.value?.youUserId,
    shopSupplierId: paramsOptions.value?.shopSupplierId,
  }),
)
watch(
  () => chatInfo.value?.url,
  async () => {
    await nextTick()
    socketUrl.value = chatInfo.value?.url
    socketInit()
  },
)
const userAvatar = computed(() => chatInfo.value?.avatarUrl)
const serviceAvatar = computed(() => chatInfo.value?.logo)
console.log('paramsOptions.value?.nickName', paramsOptions.value)
const nickName = computed(() =>
  paramsOptions.value?.nickName ? paramsOptions.value?.nickName : '客服',
)

// 商品信息
const { data: productDetail, run: productRun } = useRequest(() =>
  fetchRecGoodsDetail({
    appId: 10001,
    productId: paramsOptions.value?.productId,
    visitcode: getVisitcode(),
  }),
)
const isProduct = ref(false)
const handleProductLoad = async () => {
  try {
    await productRun()
    isProduct.value = true
    await nextTick()
    paging.value?.addChatRecordData({
      sendType: 'product',
      content: '123',
    })
  } catch (err) {
    console.error('加载商品信息失败', err)
  }
}

// 商铺信息
const { data: storeDetial, run: storeRun } = useRequest(() =>
  fetchSupplierInfo({
    shopSupplierId: paramsOptions.value?.shopSupplierId,
  }),
)

// 订单信息
const { data: orderDetial, run: orderRun } = useRequest(() =>
  UserOrderDetail({
    orderId: parseInt(paramsOptions.value?.orderId),
  }),
)
const isOrder = ref(false)
const handleOrderLoad = async () => {
  try {
    await orderRun()
    isOrder.value = true
    await nextTick()
    paging.value?.addChatRecordData({
      sendType: 'order',
      content: '123',
    })
  } catch (err) {
    console.error('加载订单信息失败', err)
  }
}

// 聊天列表
const paging = ref()
const dataList = ref<Api.Message.ChatRecordItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchChatRecord({
        pageIndex,
        pageSize,
        serviceUserId: paramsOptions.value.youUserId,
      })
      paging.value?.complete(data?.records)

      if (pageIndex === 1) {
        // await nextTick()

        // 商品
        if (paramsOptions.value?.productId) {
          await handleProductLoad()
        }

        // 订单
        if (paramsOptions.value?.orderId) {
          await handleOrderLoad()
        }
      }
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

// 输入框
const sendInputVal = ref('')

const sendContent = () => {
  if (sendInputVal.value === '') {
    uni.showToast({
      title: '发送内容不能为空！',
      icon: 'none',
    })
    return false
  }
  const data = JSON.stringify({
    to: paramsOptions.value.youUserId,
    from: paramsOptions.value?.myUserId,
    sendType: 2,
    type: 'msg',
    msgType: 0,
    content: sendInputVal.value,
  })
  const newData = JSON.parse(data)
  const item = {
    sendType: 2,
    content: newData.content,
    type: newData.type,
    msgType: 0,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    avatarUrl: userAvatar.value,
  }

  paging.value?.addChatRecordData(item)

  send(data)

  sendInputVal.value = ''
}

const sendProduct = () => {
  const params = {
    productName: productDetail?.value?.detail?.productName,
    productImg: productDetail?.value?.detail?.image?.[0]?.filePath,
    productPrice: productDetail?.value?.detail?.productPrice,
  }
  const data = JSON.stringify({
    to: paramsOptions.value?.youUserId,
    from: paramsOptions.value?.myUserId,
    sendType: 2,
    type: 'msg',
    msgType: 2,
    content: params,
  })
  const newData = JSON.parse(data)

  const item = {
    sendType: 2,
    contentJson: newData.content,
    type: newData.type,
    msgType: 2,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    avatarUrl: userAvatar.value,
  }

  paging.value?.addChatRecordData(item)

  send(data)

  isProduct.value = false
}

const sendOrder = () => {
  const params = {
    orderNum: orderDetial?.value?.detail?.product.length,
    orderPrice: orderDetial?.value?.detail?.orderPrice,
    orderNo: orderDetial?.value?.detail?.orderNo,
    createTime: orderDetial?.value?.detail?.createTime,
    orderId: orderDetial?.value?.detail?.orderId,
    productName: orderDetial?.value?.detail?.product[0].productName,
    productImg: orderDetial?.value?.detail?.product[0].productImage,
  }
  const data = JSON.stringify({
    to: paramsOptions.value?.youUserId,
    from: paramsOptions.value?.myUserId,
    sendType: 2,
    type: 'msg',
    msgType: 3,
    content: params,
  })

  const newData = JSON.parse(data)

  const item = {
    sendType: 2,
    contentJson: newData.content,
    type: newData.type,
    msgType: 3,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    avatarUrl: userAvatar.value,
  }

  paging.value?.addChatRecordData(item)

  send(data)

  isOrder.value = false
}

// 返回
const handleClickLeft = () => {
  uni.navigateBack()
}

const goOrderDetail = (orderId: string) => {
  uni.navigateTo({ url: `/pages-sub/myOrder/myOrderDetail?orderId=${orderId}` })
}

const goShop = () => {
  uni.redirectTo({
    url: `/pages-sub/home/<USER>/supplier/index?shopId=${storeDetial?.value?.detail?.shopSupplierId}&store=${storeDetial.value}`,
  })
}
</script>

<style lang="scss" scoped>
//
</style>
