<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '购物车',
  },
}
</route>

<template>
  <view class="size-full flex flex-col box-border">
    <view v-if="!state.loadding" class="flex-1 of-auto">
      <view :class="{ pb90rpx: state.tableData.length > 0 }" style="position: relative">
        <!--产品列表-->
        <template v-if="state.tableData.length > 0">
          <view class="flex justify-end pt20rpx">
            <view @click="toggleEdit">
              <button class="!text-#333333 !text-24rpx" v-if="state.isEdit">完成</button>
              <button class="!text-#333333 !text-24rpx" v-else>编辑</button>
            </view>
          </view>
          <view class="section bg-#f2f2f2 p-20rpx">
            <view
              v-for="(supplierItem, supplierIndex) in state.tableData"
              :key="supplierItem?.shopSupplierId"
            >
              <view class="overflow-hidden bg-white rounded-15rpx mb-30rpx">
                <view class="flex align-center mt-20rpx ml-20rpx" v-if="state.storeOpen === 1">
                  <checkbox-group @change="() => checkStprItem(supplierItem, supplierIndex)">
                    <label class="">
                      <checkbox
                        style="transform: scale(0.9)"
                        color="red"
                        :checked="supplierItem.checked"
                        @click.stop.prevent
                      />
                    </label>
                  </checkbox-group>

                  <view
                    @tap="
                      gotoPage(
                        `/pages/shop/shop?shopSupplierId=${supplierItem.supplier.shopSupplierId}`,
                      )
                    "
                  >
                    <i class="text-34rpx text-#333333 mx-17rpx"></i>
                    <text class="text-#333333">{{ supplierItem.supplier.name }}</text>
                  </view>
                </view>

                <wd-swipe-action
                  v-for="(item, index) in supplierItem.productList"
                  :key="item.cartId"
                  :modelValue="item.swipeAction"
                >
                  <template #default>
                    <view class="mx-20rpx flex items-center p-r-19rpx p-b-29rpx p-t-29rpx">
                      <checkbox-group @change="(e) => checkItem(item, supplierIndex, index)">
                        <label class="">
                          <checkbox
                            style="transform: scale(0.9)"
                            color="red"
                            :checked="item.checked"
                            :disabled="!state.isEdit && item.stockNum < 1"
                          />
                        </label>
                      </checkbox-group>
                      <div
                        class="flex items-center w-full"
                        @click.stop.prevent="() => goGoodsDetail(item?.productId)"
                      >
                        <div class="relative" style="overflow: hidden">
                          <image
                            :src="item.productImage"
                            class="w-150rpx h-150rpx rounded-8rpx"
                            mode="aspectFit"
                          ></image>
                          <div
                            v-if="item?.stockNum <= 0"
                            class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center pointer-events-none"
                          >
                            <span>售罄</span>
                          </div>
                        </div>
                        <view class="info">
                          <view class="title">{{ item.productName }}</view>
                          <view
                            class="describe"
                            v-if="item?.productAttr"
                            @click.stop.prevent="getProductAttr(item)"
                          >
                            {{ item.productAttr }}
                          </view>
                          <view class="describe" v-else="item?.productAttr"></view>
                          <view class="level-box count_choose">
                            <view class="price text-#FF7D26">
                              ¥
                              <text class="num">{{ item.productPrice }}</text>
                            </view>
                            <view class="min-w-150rpx flex justify-end">
                              <!-- 自定义数量输入组件 -->
                              <view
                                v-if="item?.stockNum >= 1"
                                class="flex items-center bg-#f5f5f5 rd-24rpx h-48rpx w-150rpx"
                              >
                                <!-- 减少按钮 -->
                                <view
                                  class="w-48rpx h-48rpx flex items-center justify-center rd-l-24rpx transition-colors"
                                  :class="
                                    item.totalNum > 0
                                      ? 'bg-white text-#333 cursor-pointer'
                                      : 'bg-#f5f5f5 text-#ccc cursor-not-allowed'
                                  "
                                  @tap.stop="handleReduce(item)"
                                >
                                  <wd-icon name="decrease" size="24rpx"></wd-icon>
                                </view>

                                <!-- 数量输入框 -->
                                <input
                                  class="flex-1 h-full text-center text-28rpx bg-white border-0 outline-none appearance-none"
                                  style="border: none; outline: none"
                                  type="number"
                                  :value="item.totalNum"
                                  @input="handleInput($event, item)"
                                  @blur="handleBlur(item)"
                                  @tap.stop=""
                                />

                                <!-- 增加按钮 -->
                                <view
                                  class="w-48rpx h-48rpx flex items-center justify-center rd-r-24rpx transition-colors"
                                  :class="
                                    item.totalNum < item.stockNum
                                      ? 'bg-white text-#333 cursor-pointer'
                                      : 'bg-#f5f5f5 text-#ccc cursor-not-allowed'
                                  "
                                  @tap.stop="handleAdd(item)"
                                >
                                  <wd-icon name="add" size="24rpx"></wd-icon>
                                </view>
                              </view>
                              <view v-else class="text-24rpx text-#999">商品缺货,暂无法购买</view>
                            </view>
                          </view>
                        </view>
                      </div>
                    </view>
                  </template>
                  <template #right>
                    <div class="h-100% flex flex-col">
                      <view
                        class="flex-1 flex items-center px40rpx bg-red text-white box-border"
                        @click="handleDelete(item.cartId)"
                      >
                        删除
                      </view>
                    </div>
                  </template>
                </wd-swipe-action>
              </view>
            </view>
          </view>
        </template>
        <template v-else>
          <view
            class="py-88rpx px-32rpx box-border flex flex-col justify-center items-center gap-30rpx"
          >
            <image
              class="w-384rpx h-240rpx"
              src="https://file.shanqianqi.com/image/2025/06/20/978b7dd72c29468eb3adcb54b63042b8.png"
              mode="widthFix"
            ></image>
            <text class="text-#999999 text-30rpx">购物车空空如也</text>
            <button class="w-full bg-#FF7D26 rounded-40rpx text-white text-32rpx" @tap="gotoShop">
              去购物
            </button>
          </view>
        </template>

        <!--推荐-->
        <recommendProduct :location="10"></recommendProduct>

        <!--底部按钮-->
        <div class="h-90rpx"></div>
        <view
          class="flex justify-between items-center text-20rpx bottom-btns"
          :class="{ isAuto: state.isAuto }"
          :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
          v-if="state.tableData.length > 0"
        >
          <checkbox-group @change="onCheckedAll">
            <label class="mr20rpx flex items-center">
              <checkbox
                style="transform: scale(0.9)"
                color="red"
                :checked="state.checkedAll"
                @click.stop
              />
              <span class="text-24rpx text-#333333">全选</span>
            </label>
          </checkbox-group>
          <view class="flex items-center box-border pr20rpx" v-if="!state.isEdit">
            <view class="flex items-center mr20rpx">
              <text class="text-24rpx gray9">合计：</text>
              <view class="price text-24rpx text-#FF7D26">
                ¥
                <text class="num text-32rpx">{{ state.totalPrice }}</text>
              </view>
            </view>
            <button class="text-white bg-#ff5704 text-26rpx buy-btn" @tap="Submit">去结算</button>
          </view>
          <view class="pr20rpx" v-else>
            <button
              @tap="onDelete"
              class="delete-btn px60rpx bg-#ff5704 text-white text-26rpx rounded-30rpx"
            >
              删除
            </button>
          </view>
        </view>
      </view>
    </view>
    <!-- <request-loading :loadding="state.isloadding"></request-loading>
    <tabBar></tabBar> -->

    <wd-popup
      v-model="state.showPrePay"
      position="bottom"
      safe-area-inset-bottom
      lockScroll
      custom-style="min-height: 200px;max-height:600px"
      @close="handleClose"
    >
      <div class="grid grid-cols-1 p-30rpx box-border gap-y-10px">
        <div
          class="flex items-start gap-x-10px pb10px box-border border-b border-b-solid border-b-#eeeeee"
        >
          <wd-img
            width="180rpx"
            :src="
              productDetail?.detail?.skuList?.find((item) => item?.specSkuId === specSkuId)
                ?.imagePath || productDetail?.detail?.image?.[0]?.filePath
            "
            height="180rpx"
            mode="aspectFit"
          ></wd-img>
          <div class="flex flex-col gap-y-10px">
            <div class="flex items-baseline text-#FF7D26">
              <span class="text-20rpx font-500">¥</span>
              <span class="text-40rpx font-bold">
                {{ showPrice?.split('.')?.[0] }}
              </span>
              <span class="text-24rpx font-bold">.{{ showPrice?.split('.')?.[1] }}</span>
              <template v-if="productDetail?.detail?.specType === 20 && !specSkuId">
                <span class="text-40rpx font-bold mx-5px">-</span>
                <span class="text-20rpx font-500">¥</span>
                <span class="text-40rpx font-bold">
                  {{ productDetail?.detail?.highPrice?.split('.')?.[0] }}
                </span>
                <span class="text-24rpx font-bold">
                  .{{ productDetail?.detail?.highPrice?.split('.')?.[1] }}
                </span>
              </template>
            </div>
            <div class="text-24rpx text-#999999">
              库存：{{
                productDetail?.detail?.specType === 20
                  ? productDetail?.specData?.specList?.find(
                      (spc) => spc?.specSkuId === `${specSkuId}`,
                    )?.productStock ?? '-'
                  : productDetail?.detail?.productStock ?? '-'
              }}件
            </div>
          </div>
        </div>

        <div
          v-if="productDetail?.detail?.specType === 20 && productDetail?.specData"
          class="grid grid-cols-1 gap-y-5px grid-auto-rows-min"
        >
          <div
            class="flex flex-col gap-y-5px"
            v-for="(item, index) in productDetail?.specData?.specAttr"
            :key="item?.groupName"
          >
            <span class="text-26rpx text-#333333">{{ item?.groupName }}</span>
            <div class="flex items-center gap-10px flex-wrap">
              <button
                v-for="(ele, skuIndex) in item?.specItems"
                :key="ele?.specValue"
                @click="() => handleSpecChoose(ele, item?.groupName)"
                :disabled="!canSelectGroup(index, skuIndex)"
                class="custom-btn"
                :class="[
                  selectedSpecs[item.groupName] === ele.itemId ? 'btn-primary' : 'btn-info',
                  !canSelectGroup(index, skuIndex) ? 'btn-disabled' : '',
                ]"
                :title="ele?.specValue"
              >
                {{ ele?.specValue }}
              </button>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="text-24rpx text-#999999">数量</div>
          <wd-input-number
            v-model="buyNubmer"
            :min="1"
            :max="productDetail?.detail?.productStock"
          />
        </div>
        <wd-button
          :disabled="isOutOfStock"
          @click="goConfirm"
          custom-class="!w-full confirmBtn"
          block
        >
          确认
        </wd-button>
      </div>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import recommendProduct from '@/components/recommendProduct/recommendProduct.vue'
import {
  addCart,
  cartPreBuy,
  deleteCart,
  fetchUserCartList,
  removeCart,
  fetchRecGoodsDetail,
} from '@/service'
import { useUserStore } from '@/store'
import { useCartStore } from '@/store/cart'
import { useConfirmOrderStore } from '@/store/confirm-order'
import { debounce } from '@/utils'
import { getPlatform } from '@/utils'

const userStore = useUserStore()
const cartStore = useCartStore()
const confirmOrderStore = useConfirmOrderStore()
const { safeAreaInsets } = uni.getSystemInfoSync()

defineOptions({
  name: 'Cart',
})

// 类型定义

interface CartState {
  isloadding: boolean
  scrollviewHigh: number
  loadding: boolean
  isEdit: boolean
  tableData: Api.Home.CartListItem[]
  arrIds: string[]
  checkedAll: boolean
  totalPrice: string
  totalProduct: number
  storeOpen: number
  isAuto: boolean
  checkLst: Array<any>
  showPrePay: boolean
  oldGoodInfo: any
}

// 状态初始化
const state = reactive<CartState>({
  isloadding: true,
  scrollviewHigh: 0,
  loadding: true,
  isEdit: false,
  tableData: [],
  arrIds: [],
  checkedAll: false,
  totalPrice: '0.00',
  totalProduct: 0,
  storeOpen: 1,
  isAuto: false,
  //选中的商品
  checkLst: [],
  //显示规格弹窗
  showPrePay: false,
  //此前加购的商品信息
  oldGoodInfo: null,
})
const productDetail = ref<Api.Home.GoodDetail>()
const buyNubmer = ref(1)
const showPrice = ref('')
//选中的skuID
const specSkuId = ref<string>()
const selectedSpecs = ref<Record<string, number>>({}) // 以 groupName 为 key，itemId 为值

// 生命周期钩子
onShow(() => {
  getData()
  console.log('🚀 ~ userStore:', userStore)
})
//是否无商品库存
const isOutOfStock = computed(() => {
  return productDetail.value?.detail?.productStock <= 0
})
//滑动删除关闭前的操作
const goConfirm = async () => {
  const specAttrCount = productDetail.value?.specData?.specAttr?.length || 0

  if (
    Object.keys(selectedSpecs.value).length < specAttrCount &&
    productDetail.value?.detail?.specType === 20
  ) {
    uni.showToast({ title: '请选择规格', icon: 'none' })
    return
  }
  if (specSkuId.value == state.oldGoodInfo?.specSkuId) {
    state.showPrePay = false
    return
  }
  handleAddCart()
}
//加购
const handleAddCart = async () => {
  // 检查多规格商品是否已选择规格
  const specAttrCount = productDetail.value?.specData?.specAttr?.length || 0
  if (
    productDetail.value?.detail?.specType === 20 &&
    Object.keys(selectedSpecs.value).length < specAttrCount
  ) {
    uni.showToast({ title: '请选择规格', icon: 'none' })
    return
  }
  const dataBody = {
    appId: import.meta.env.VITE_APPID,
    productId: productDetail.value?.detail?.productId,
    totalNum: buyNubmer.value,
    specSkuId: specSkuId.value,
    token: userStore?.token ?? '',
  }
  try {
    await addCart(dataBody)
    await getData()
  } catch (error) {
    console.log('error', error)
    uni.showToast({
      title: error.data.msg || '添加失败，请稍后再试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    state.showPrePay = false
  }
}
//关闭规格弹窗
const handleClose = () => {
  state.showPrePay = false
}
let signInTimeout: ReturnType<typeof setTimeout> | null = null
//点击购物车规格重选规格
const getProductAttr = (item: any) => {
  if (signInTimeout) clearTimeout(signInTimeout)
  specSkuId.value = ''
  showPrice.value = ''
  productDetail.value = null
  signInTimeout = setTimeout(async () => {
    try {
      state.showPrePay = true
      state.oldGoodInfo = item
      await fetchDetail(state.oldGoodInfo)
    } catch (error) {}
  }, 300)
}

const handleSpecChoose = (item: { itemId: number; specValue: string }, groupName: string) => {
  selectedSpecs.value[groupName] = item.itemId

  // 更新价格显示
  const specAttr = productDetail.value?.specData?.specAttr || []
  const specList = productDetail.value?.specData?.specList || []

  if (Object.keys(selectedSpecs.value).length === specAttr.length) {
    const idList = specAttr.map((group) => selectedSpecs.value[group.groupName])
    const skuId = idList.join('_')
    const validSku = specList.find((sku) => sku.specSkuId === skuId && sku.productStock > 0)

    if (validSku) {
      showPrice.value = validSku.specForm.productPrice
      specSkuId.value = validSku.specSkuId
    }
  }
}
const canSelectGroup = (groupIndex: number, skuIndex: number) => {
  const specData = productDetail.value?.specData
  if (!specData) return false

  const currentItemId = specData.specAttr[groupIndex].specItems[skuIndex].itemId
  const specList = specData.specList || []

  // 1. 一级规格
  if (groupIndex === 0) {
    // 只要有任意SKU第一个规格等于当前itemId且有库存即可
    return specList.some((sku) => {
      const skuIds = sku.specSkuId.split('_')
      return skuIds[0] == currentItemId && sku.productStock > 0
    })
  }

  // 2. 多级规格
  // 检查前面规格是否都已选
  const selectedIds: (string | number)[] = []
  for (let i = 0; i < groupIndex; i++) {
    const groupName = specData.specAttr[i]?.groupName
    const selected = selectedSpecs.value[groupName]
    if (!selected) return false
    selectedIds.push(selected)
  }

  // 检查是否有SKU前面规格和当前规格都匹配且有库存
  return specList.some((sku) => {
    const skuIds = sku.specSkuId.split('_')
    // 前面规格必须完全匹配
    for (let i = 0; i < selectedIds.length; i++) {
      if (skuIds[i] != selectedIds[i]) return false
    }
    // 当前规格必须匹配
    if (skuIds[groupIndex] != currentItemId) return false
    // 有库存
    return sku.productStock > 0
  })
}
const fetchDetail = async (obj: any) => {
  const { data } = await fetchRecGoodsDetail({
    appId: import.meta.env.VITE_APPID,
    productId: obj?.productId,
  })
  showPrice.value = data?.detail?.productPrice
  productDetail.value = data
  if (data?.detail?.specType === 20 && data?.specData) {
    const specAttr = data.specData.specAttr
    const specList = data.specData.specList || []
    if (obj?.stockNum > 0) {
      const skus = specList.find((item) => item.specSkuId === obj?.specSkuId)
      const skuIds = skus.specSkuId.split('_')
      // 自动选择对应的规格
      specAttr.forEach((group, index) => {
        selectedSpecs.value[group.groupName] = Number(skuIds[index])
      })
      showPrice.value = skus.specForm.productPrice
      specSkuId.value = skus.specSkuId
      console.log('showPrice.value', showPrice.value)
      console.log('specSkuId.value', specSkuId.value)
    } else {
      const firstValidSku = specList.find((sku) => sku.productStock > 0)
      if (firstValidSku) {
        const skuIds = firstValidSku.specSkuId.split('_')
        // 自动选择对应的规格
        specAttr.forEach((group, index) => {
          selectedSpecs.value[group.groupName] = Number(skuIds[index])
        })
        showPrice.value = firstValidSku.specForm.productPrice
        specSkuId.value = firstValidSku.specSkuId
      } else {
        // 如果没有有库存的SKU，显示默认价格范围
        showPrice.value = `${data.detail.productPrice}-${data.detail.highPrice}`
      }
    }
  } else if (
    data?.detail.specType === 10 &&
    data?.detail.skuList &&
    data?.detail.skuList.length > 0
  ) {
    specSkuId.value = data?.detail.skuList[0].specSkuId
  }
  // // 自动选择有库存的规格
  // if (data?.detail?.specType === 20 && data?.specData) {
  //   const specAttr = data.specData.specAttr
  //   const specList = data.specData.specList || []

  //   // 找到第一个有库存的SKU
  //   const firstValidSku = specList.find((sku) => sku.productStock > 0)
  //   if (firstValidSku) {
  //     const skuIds = firstValidSku.specSkuId.split('_')
  //     // 自动选择对应的规格
  //     specAttr.forEach((group, index) => {
  //       selectedSpecs.value[group.groupName] = Number(skuIds[index])
  //     })
  //     showPrice.value = firstValidSku.specForm.productPrice
  //     specSkuId.value = firstValidSku.specSkuId
  //   } else {
  //     // 如果没有有库存的SKU，显示默认价格范围
  //     showPrice.value = `${data.detail.productPrice}-${data.detail.highPrice}`
  //   }
  // }
  // if (data?.detail.specType === 10 && data?.detail.skuList && data?.detail.skuList.length > 0) {
  //   specSkuId.value = data?.detail.skuList[0].specSkuId
  //   console.log('无规格', data?.detail?.specType, data?.detail.skuList[0].specSkuId)
  // }
}
// 方法实现
const toggleEdit = () => {
  state.isEdit = !state.isEdit
  if (state.isEdit) {
    // 进入编辑模式时，保留有库存商品的选中状态，无库存商品不选中
    state.tableData.forEach((item) => {
      item.productList.forEach((product) => {
        if (product.stockNum <= 0) {
          product.checked = false
        }
      })
    })
    // 不重置 state.checkedAll，保留当前全选状态
    onUpdateChecked()

    // 更新每个店铺的选中状态
    state.tableData.forEach((_, index) => {
      onUpsupChecked(state.tableData, index)
    })

    updateCheckedAll()
  } else {
    // 退出编辑模式时，自动取消无库存商品的选中
    state.tableData.forEach((item) => {
      item.productList.forEach((product) => {
        if (product.stockNum <= 0) {
          product.checked = false
        }
      })
    })
    onUpdateChecked()

    // 更新每个店铺的选中状态
    state.tableData.forEach((_, index) => {
      onUpsupChecked(state.tableData, index)
    })

    updateCheckedAll()
  }
}

const getData = async () => {
  state.isloadding = true
  state.storeOpen = 1

  try {
    const { data } = await fetchUserCartList({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
    })
    let tableList = data?.productList ?? []
    state.tableData = tableList.map((supplier) => {
      return {
        ...supplier,
        productList: supplier.productList.map((product) => {
          return {
            ...product,
            swipeAction: 'close',
          }
        }),
      }
    })

    cartStore.setCart(data)

    _initGoodsChecked()
  } catch (error) {
  } finally {
    state.isloadding = false
    state.loadding = false
  }
}

const _initGoodsChecked = () => {
  const checkedData = getCheckedData()
  let productCount = 0

  state.tableData.forEach((item) => {
    item.productList.forEach((product) => {
      productCount++
      // 只在非编辑模式下强制无库存商品不选中
      if (!state.isEdit && product.stockNum <= 0) {
        product.checked = false
      } else {
        product.checked = inArray(`${product.productId}_${product.specSkuId}`, checkedData)
      }
    })
  })

  state.totalProduct = productCount

  state.tableData.forEach((_, index) => {
    onUpsupChecked(state.tableData, index)
  })

  updateTotalPrice()
  updateCheckedAll()
}

const inArray = (search: string, array: string[]): boolean => {
  return array.includes(search)
}
const checkItem = (item: Api.Home.CartListProductItem, supplierIndex: number, index: number) => {
  state.tableData[supplierIndex].productList[index] = item

  nextTick(() => {
    // 切换商品选中状态
    item.checked = !item.checked

    // 更新店铺选中状态
    onUpsupChecked(state.tableData, supplierIndex)

    // 更新本地存储的选中状态
    onUpdateChecked()

    // 更新总价
    updateTotalPrice()

    // 更新全选状态
    updateCheckedAll()
  })
}

const onUpsupChecked = (item: Api.Home.CartListItem[], index: number) => {
  if (state.isEdit) {
    // 编辑模式下，判断所有商品
    const allProducts = item[index].productList
    const supplierFlag = allProducts.length > 0 && allProducts.every((product) => product.checked)
    item[index].checked = supplierFlag
  } else {
    // 非编辑模式下，只判断有库存的商品
    const hasStockProducts = item[index].productList.filter((product) => product.stockNum > 0)
    const supplierFlag =
      hasStockProducts.length > 0 && hasStockProducts.every((product) => product.checked)
    item[index].checked = supplierFlag
  }
}

const onUpdateChecked = () => {
  const checkedData = state.tableData.flatMap((item) =>
    item.productList
      .filter((product) => product.checked)
      .map((product) => `${product.productId}_${product.specSkuId}`),
  )

  uni.setStorageSync('CheckedData', checkedData)
}

const checkStprItem = (itemp: Api.Home.CartListItem, supplierIndex: number) => {
  console.log('🚀 ~ checkStprItem ~ itemp:', itemp)
  // 编辑模式下，所有商品都可选，否则只操作有库存商品
  const filterFn = state.isEdit ? (item) => true : (item) => item.stockNum > 0
  const filteredProducts = itemp.productList.filter(filterFn)
  const allChecked = filteredProducts.length > 0 && filteredProducts.every((item) => item.checked)
  const newChecked = !allChecked

  // 设置店铺选中状态
  itemp.checked = newChecked

  // 设置商品选中状态
  itemp.productList.forEach((item) => {
    // 编辑模式下所有商品都可以选中，非编辑模式下只有有库存商品可以选中
    if (state.isEdit || item.stockNum > 0) {
      item.checked = newChecked
    } else if (!state.isEdit) {
      item.checked = false
    }
  })

  updateTotalPrice()
  onUpdateChecked()
  updateCheckedAll()
}

const onCheckedAll = () => {
  // 这里不需要再次判断是否全选，因为点击checkbox时状态已经改变
  // 直接使用当前的checkedAll状态来设置所有商品
  const newChecked = !state.checkedAll

  if (state.isEdit) {
    // 编辑模式下，全选/反选所有商品
    state.tableData.forEach((item) => {
      item.checked = newChecked
      item.productList.forEach((product) => {
        product.checked = newChecked
      })
    })
  } else {
    // 非编辑模式下，只全选/反选有库存商品
    state.tableData.forEach((item) => {
      // 店铺checked只看有库存商品
      const hasStockProducts = item.productList.filter((p) => p.stockNum > 0)
      item.checked = hasStockProducts.length > 0 && hasStockProducts.every((p) => newChecked)

      item.productList.forEach((product) => {
        if (product.stockNum > 0) {
          product.checked = newChecked
        } else {
          product.checked = false
        }
      })
    })
  }

  state.checkedAll = newChecked
  updateTotalPrice()
  onUpdateChecked()
}
const updateCheckedAll = () => {
  if (state.isEdit) {
    // 编辑模式下，所有商品都参与全选
    const allCount = state.tableData.reduce((sum, supplier) => sum + supplier.productList.length, 0)
    const checkedCount = state.tableData.reduce(
      (sum, supplier) => sum + supplier.productList.filter((item) => item.checked).length,
      0,
    )
    state.checkedAll = allCount > 0 && checkedCount === allCount
  } else {
    // 非编辑模式下，只统计有库存商品
    const allCount = state.tableData.reduce(
      (sum, supplier) => sum + supplier.productList.filter((item) => item.stockNum > 0).length,
      0,
    )
    const checkedCount = state.tableData.reduce(
      (sum, supplier) =>
        sum + supplier.productList.filter((item) => item.checked && item.stockNum > 0).length,
      0,
    )
    state.checkedAll = allCount > 0 && checkedCount === allCount
  }
}

const updateTotalPrice = () => {
  const totalPrice = state.tableData.reduce((acc, item) => {
    return (
      acc +
      item.productList.reduce((pAcc, product) => {
        return (
          pAcc + (product.checked ? Number(product.totalNum) * Number(product.productPrice) : 0)
        )
      }, 0)
    )
  }, 0)

  state.totalPrice = totalPrice.toFixed(2)
}
watch(
  () => confirmOrderStore.selectedAddressId,
  (v) => {
    if (v !== null && v !== undefined) handlePreBuy()
  },
)

const handlePreBuy = async (callback?: () => void) => {
  const arrIds = getCheckedData()

  if (arrIds.length === 0) {
    uni.showToast({ title: '请选择商品', icon: 'none' })
    return
  }

  const dataBody = {
    appId: import.meta.env.VITE_APPID,
    delivery: 10,
    cartIds: arrIds.join(','),
    isUsePoints: 1,
    paySource: getPlatform(),
    storeId: 0,
  }

  try {
    const { data } = await cartPreBuy(dataBody)

    confirmOrderStore.setOrderInfo(data)

    callback && callback()
  } catch (error) {}
}
const Submit = async () => {
  handlePreBuy(() =>
    uni.navigateTo({ url: '/pages-sub/goods/confirm-order/index?orderType=cart&storeId=0' }),
  )
}

// 自定义数量输入组件的方法
const handleAdd = debounce(async (item: Api.Home.CartListProductItem) => {
  if (item.totalNum >= item.stockNum) return

  uni.showLoading({ title: '加载中' })
  try {
    await addCart({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
      productId: item.productId,
      specSkuId: item.specSkuId,
      totalNum: 1,
    })

    getData()
  } catch (error) {
  } finally {
    uni.hideLoading()
  }
}, 500)

const handleReduce = debounce(async (item: Api.Home.CartListProductItem) => {
  if (item.totalNum <= 0) return

  uni.showLoading({ title: '加载中' })
  try {
    await removeCart({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
      productId: item.productId,
      specSkuId: item.specSkuId,
    })

    getData()
  } catch (error) {
  } finally {
    uni.hideLoading()
  }
}, 500)

// 处理输入框输入
const handleInput = (event: any, item: Api.Home.CartListProductItem) => {
  const value = parseInt(event.detail.value) || 0
  // 临时更新显示值，不立即调用接口
  item.totalNum = Math.max(0, Math.min(value, item.stockNum))
}

// 处理输入框失焦，调用接口更新数量
const handleBlur = debounce(async (item: Api.Home.CartListProductItem) => {
  const newNum = item.totalNum

  if (newNum <= 0) {
    // 如果数量为0或负数，删除商品
    await handleDelete(item.cartId)
    return
  }

  if (newNum > item.stockNum) {
    item.totalNum = item.stockNum
    uni.showToast({
      title: `库存不足，最多可购买${item.stockNum}件`,
      icon: 'none',
    })
  }

  // 调用接口更新数量
  await updateCartQuantity(item, newNum)
}, 800)

// 更新购物车数量的接口调用
const updateCartQuantity = async (item: Api.Home.CartListProductItem, newQuantity: number) => {
  uni.showLoading({ title: '更新中' })
  try {
    // 这里可以调用具体的更新数量接口
    // 如果没有直接的更新接口，可以通过删除后重新添加来实现

    // 方案1：如果有直接更新数量的接口
    // await updateCartItemQuantity({
    //   cartId: item.cartId,
    //   quantity: newQuantity
    // })

    // 方案2：通过删除后重新添加（当前使用这种方式）
    await removeCart({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
      productId: item.productId,
      specSkuId: item.specSkuId,
    })

    if (newQuantity > 0) {
      await addCart({
        appId: import.meta.env.VITE_APPID,
        token: userStore.token,
        productId: item.productId,
        specSkuId: item.specSkuId,
        totalNum: newQuantity,
      })
    }

    await getData()
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 保留原有方法以兼容其他地方的调用
const addFunc = handleAdd
const reduceFunc = handleReduce
const handleDelete = async (cartId: any) => {
  uni.showLoading({ title: '加载中' })
  try {
    await deleteCart({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
      cartIds: [cartId],
    })
    getData()
    onDeleteEvent([cartId])
    uni.hideLoading()
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: error.msg || '删除失败',
      icon: 'none',
      duration: 2000,
    })
  }
}
const onDelete = () => {
  const cartIds = getCheckedIds()
  const cartIdsArr = Array.isArray(cartIds) ? cartIds : cartIds ? cartIds.split(',') : []
  if (!cartIdsArr.length) {
    uni.showToast({ title: '您还没有选择商品', icon: 'none' })
    return
  }
  uni.showModal({
    title: '提示',
    content: '您确定要移除选择的商品吗?',
    success: async (e) => {
      if (e.confirm) {
        try {
          await deleteCart({
            appId: import.meta.env.VITE_APPID,
            token: userStore.token,
            cartIds: cartIdsArr,
          })

          getData()
          onDeleteEvent(cartIdsArr)
        } catch (error) {}
      }
    },
  })
}

const getCheckedData = (): string[] => {
  return uni.getStorageSync('CheckedData') || []
}

const getCheckedIds = (): string | number[] => {
  return state.tableData.flatMap((item) =>
    item.productList.filter((product) => product.checked).map((product) => product.cartId),
  )
}

const onDeleteEvent = (cartIds: Array<string | number>) => {
  state.tableData.forEach((supplierItem) => {
    supplierItem.productList = supplierItem.productList.filter(
      (product) => !cartIds.includes(product.cartId),
    )
  })
  state.tableData = state.tableData.filter((item) => item.productList.length > 0)
  nextTick(() => {
    onUpdateChecked()
  })
}

const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}

const gotoShop = () => {
  gotoPage('/pages-sub/home/<USER>/index')
}

const goGoodsDetail = (id: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}&from=mall` })
}

// 页面显示时隐藏首页按钮
onShow(() => {
  // #ifdef MP-WEIXIN
  uni.hideHomeButton()

  // #endif
})
</script>

<style lang="scss" scoped>
:deep(.wd-popup) {
  z-index: 2000 !important;
}
:deep(.topIcon) {
  color: grey !important;
}
:deep(.topActiveIcon) {
  color: red !important;
}
$footer-tabBar-height: 50px;

page {
  background-color: #f2f2f2;
}

.foot_ {
  height: $footer-tabBar-height;
  width: 100%;
}

.card {
  padding-bottom: $footer-tabBar-height;
}

.card .scroll-Y {
  position: relative;
}

.card .checkbox {
  transform: scale(0.7);
}

.address-bar {
  padding: 20rpx;
  background-color: #f2f2f2;
}

.address-bar button {
  border: none;
  background: none;
  color: #333333;
}

.section .info {
  flex: 1;
  padding-left: 30rpx;
  box-sizing: border-box;
  overflow: hidden;
}

.section .title {
  width: 100%;
  font-size: 26rpx;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.vender .list .describe {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.section .describe {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  font-size: 24rpx;
  color: #999999;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.section .price {
  font-size: 24rpx;
}

.section .price .num {
  padding: 0 4rpx;
  font-size: 32rpx;
}

.section .level-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section .level-box .key {
  font-size: 24rpx;
  color: #999999;
}

.section .level-box .num-wrap {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.section .level-box .icon-box {
  width: 33rpx;
  height: 33rpx;
  border: 1px solid #c5c5c5;
  background: #f2f2f2;
}

.section .level-box .icon-box .gray {
  color: #cccccc;
}

.section .level-box .text-wrap {
  //height: 33rpx;
  border: none;
  background: none;
}

.section .level-box .text-wrap input {
  padding: 0 4rpx;
  height: 33rpx;
  line-height: 1;
  width: 40rpx;
  font-size: 32rpx;
  text-align: center;
  display: flex;
  align-items: center;
  min-height: 33rpx;
}

.bottom-btns {
  position: fixed;
  width: 100%;
  padding: 30rpx 20rpx 0 20rpx;
  box-sizing: border-box;
  // height: 90rpx;
  bottom: 0;
  left: 0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  background: #ffffff;
  z-index: 1000;
}

.bottom-btns .buy-btn {
  margin: 0;
  padding: 0 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
}

.cart_none .cart_none_img {
  width: 348rpx;
  height: 222rpx;
}

.none_btn {
  font-size: 32rpx;
  border-radius: 40rpx;
}

.add_icon,
.reduce_icon {
  width: 32rpx;
  height: 32rpx;
}

//规格按钮
.custom-btn {
  padding: 10rpx 24rpx;
  font-size: 24rpx;
  border-radius: 12rpx;
  margin: 0;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  line-height: 1.4;
  background-color: transparent;
  border: 1rpx solid #e5e5e5;
  color: #666;
  transition: all 0.2s;

  &.btn-primary {
    border-color: #ff5704;
    color: #ff5704;
    background-color: #fff7f0;
  }

  &.btn-info {
    border-color: #e5e5e5;
    color: #666;
    background-color: #f5f5f5;
  }

  &.btn-disabled {
    background-color: #cccccc !important;
    color: white !important;
    border-color: #eeeeee !important;
    cursor: not-allowed;
  }
}
</style>
